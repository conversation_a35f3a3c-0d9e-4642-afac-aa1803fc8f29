# ===========================================
# FastAPI Auth 系统配置文件
# 非敏感的结构化配置
# ===========================================

# ===========================================
# 应用基本设置
# ===========================================
app_name: "FastAPI Auth System"
app_port: 8088
app_version: "1.0.0"
app_reload: true
app_workers: 1
app_ip_location_query: true
app_same_time_login: true
app_host: "0.0.0.0"

# 启动时初始化设置
app_init_base_data_on_startup: true

# 用户代理列表
app_user_agents:
  - "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
  - "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
  - "Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"

# CORS 配置
app_cors_origins:
  - "*"
app_cors_credentials: true

# HTTP 客户端设置
app_http_timeout: 30.0
app_download_timeout: 30.0

# NAS 设置
app_nas_base_path: "/app/videos"

# ===========================================
# 数据库配置（非敏感部分）
# ===========================================
db_type: "postgresql"
db_echo: false
db_max_overflow: 10
db_pool_size: 20
db_pool_recycle: 3600
db_pool_timeout: 30
db_push_to_db: true

# ===========================================
# Redis 配置（非敏感部分）
# ===========================================
redis_decode_responses: true
redis_max_connections: 20
redis_socket_connect_timeout: 5
redis_socket_timeout: 5
redis_retry_on_timeout: true
redis_health_check_interval: 30

# ===========================================
# JWT 配置（非敏感部分）
# ===========================================
#jwt_algorithm: "HS256"
#jwt_access_token_expire_time: 30      # 分钟
#jwt_refresh_token_expire_time: 10080  # 分钟 (7天)

# ===========================================
# Notion 集成配置（非敏感部分）
# ===========================================
notion_push_to_notion: false