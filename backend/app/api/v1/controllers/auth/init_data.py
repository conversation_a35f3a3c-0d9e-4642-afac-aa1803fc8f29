"""
数据初始化管理API
"""
from fastapi import APIRouter, HTTPException, status
from loguru import logger

from backend.app.core.deps import AsyncSessionDep
from backend.app.services.init_data_service import init_admin, init_roles, init_permissions, init_admin_role_permission

router = APIRouter(
    prefix="/init",
    tags=["Data Initialization"],
    responses={401: {"description": "Unauthorized"}},
)


@router.post("/all")
async def initialize_all_data(db: AsyncSessionDep):
    """
    初始化所有基础数据
    包括：角色、权限、角色权限关系、管理员用户

    注意：这个接口应该只在系统首次部署时调用
    """
    try:
        # 按顺序初始化所有基础数据
        logger.info("Starting comprehensive data initialization...")

        # 1. 初始化角色
        role_stats = await init_roles(db)

        # 2. 初始化权限
        permission_stats = await init_permissions(db)

        # 3. 初始化管理员角色权限
        admin_role_result = await init_admin_role_permission(db)

        # 4. 创建管理员用户
        admin_user_result = await init_admin(db)

        return {
            "success": True,
            "message": "All data initialization completed",
            "details": {
                "roles": role_stats,
                "permissions": permission_stats,
                "admin_role_permissions": admin_role_result,
                "admin_user": admin_user_result
            }
        }
    except Exception as e:
        logger.error(f"Data initialization failed: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Data initialization failed: {str(e)}"
        )


@router.post("/roles")
async def initialize_roles(db: AsyncSessionDep):
    """
    初始化角色数据
    """
    try:
        result = await init_roles(db)
        return {"success": True, "message": "Roles initialized successfully", "stats": result}
    except Exception as e:
        logger.error(f"Roles initialization failed: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Roles initialization failed: {str(e)}"
        )


@router.post("/permissions")
async def initialize_permissions(db: AsyncSessionDep):
    """
    初始化权限数据
    """
    try:
        result = await init_permissions(db)
        return {"success": True, "message": "Permissions initialized successfully", "stats": result}
    except Exception as e:
        logger.error(f"Permissions initialization failed: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Permissions initialization failed: {str(e)}"
        )


@router.post("/admin-role-permissions")
async def initialize_admin_role_permissions(db: AsyncSessionDep):
    """
    为管理员角色分配所有权限
    """
    try:
        result = await init_admin_role_permission(db)
        return result
    except Exception as e:
        logger.error(f"Admin role permissions initialization failed: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Admin role permissions initialization failed: {str(e)}"
        )


@router.post("/admin-user")
async def initialize_admin_user(db: AsyncSessionDep):
    """
    创建默认管理员用户
    用户名: admin
    密码: admin
    角色: ADMIN (拥有所有权限)
    """
    try:
        result = await init_admin(db)
        return result
    except Exception as e:
        logger.error(f"Admin user creation failed: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Admin user creation failed: {str(e)}"
        )
