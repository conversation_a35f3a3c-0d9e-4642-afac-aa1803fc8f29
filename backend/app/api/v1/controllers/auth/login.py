"""
登录相关API
"""
from fastapi import APIRouter, Depends, HTTPException, status, Cookie, Response
from fastapi.security import OAuth2PasswordRequestForm

from typing import Annotated, Optional
from loguru import logger

from backend.app.services.auth_service import TokenService, AuthService
from backend.app.core.deps import AsyncSessionDep
from backend.app.models import User, Role
from backend.app.config.enums import RoleType
from backend.app.api.v1.schemas.user import UserCreate, AccessTokenResponse
from backend.app.repo.user_repository import UserRepository, RoleRepository
from backend.app.repo.token_repository import RefreshTokenRepository, get_refresh_token_repository
from backend.app.config.env import jwt_settings


router = APIRouter(
    responses={401: {"description": "Unauthorized"}},
)


@router.post("/login")
async def login(
    response: Response,
    form_data: Annotated[OAuth2PasswordRequestForm, Depends()],
    db: AsyncSessionDep,
    token_repo: RefreshTokenRepository = Depends(get_refresh_token_repository)
):
    try:
        user = await AuthService.verify_user(form_data.username, form_data.password, db)
        if not user:
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="Incorrect username or password",
                headers={"WWW-Authenticate": "Bearer"},
            )
        access_token = TokenService.create_access_token({"sub": user.username})
        refresh_token_cookie = TokenService.create_refresh_token({"sub": user.username})


        # 通过依赖注入的仓储存储 refresh token
        success = await token_repo.store_refresh_token(user.username, refresh_token_cookie)
        if not success:
            logger.warning(f"Failed to store refresh token for user: {user.username}")
            # 可以选择是否因此失败，或者只是记录警告

        # 设置 HttpOnly Cookie
        response.set_cookie(
            key="refresh_token",
            value=refresh_token_cookie,
            max_age=jwt_settings.jwt_refresh_token_expire_time * 24 * 60 * 60,  # 转换为秒
            httponly=True,  # 防止 XSS
            secure=False,   # 开发环境设为False，生产环境应设为True
            samesite="lax", # CSRF 保护
            path="/"        # 确保 cookie 在整个域名下可用
        )

        return AccessTokenResponse(access_token=access_token, token_type="bearer")

    except HTTPException:
        # 重新抛出 HTTPException，保持原始状态码
        raise
    except Exception as e:
        # 其他异常转换为 500 错误
        raise HTTPException(status_code=500, detail=f"登录失败: {str(e)}")


@router.post("/logout")
async def logout(
    response: Response,
    token_repo: RefreshTokenRepository = Depends(get_refresh_token_repository),
    refresh_token_cookie: Optional[str] = Cookie(None, alias="refresh_token", include_in_schema=False)
):
    """用户登出"""
    username = "unknown"  # 默认值

    try:
        # 如果有 refresh token，撤销它
        if refresh_token_cookie:
            try:
                payload = TokenService.decode_token(refresh_token_cookie)
                jti = payload.get("jti")
                username = payload.get("sub", "unknown")  # 从 token 中获取用户名

                if jti:
                    await token_repo.revoke_refresh_token(jti)
                    logger.info(f"Refresh token revoked for user: {username}")
                else:
                    logger.warning("No JTI found in refresh token")
            except Exception as e:
                # 即使 token 无效，也继续登出流程
                logger.warning(f"Failed to revoke refresh token during logout: {e}")
        else:
            logger.info("No refresh token found during logout")

    except Exception as e:
        logger.error(f"Logout error: {e}")

    finally:
        # 无论如何都要清除 cookie
        response.delete_cookie(key="refresh_token")
        logger.info(f"Logout completed for user: {username}")

    return {"message": "Successfully logged out"}

@router.post("/signup")
async def signup(*, user_data: UserCreate, db: AsyncSessionDep):
    # 在这里实现用户注册逻辑
    try:
        # 检查用户名是否已存在
        user_repo = UserRepository(db)
        user = await user_repo.get_one_by_username(user_data.username)
        logger.info(f"用户 {user_data.username} 注册")
        if user:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Username already exists",
            )

        # 创建新用户
        hashed_password = AuthService.get_password_hash(user_data.password)
        new_user = User(username=user_data.username, password=hashed_password)

        # 获取默认的 USER 角色
        role_repo = RoleRepository(db)
        user_role = await role_repo.get_one_by_name(RoleType.USER.value)

        if not user_role:
            # 如果角色不存在，创建它（通常不应该发生，因为角色应该在初始化时创建）
            logger.warning(f"角色 {RoleType.USER.value} 不存在，正在创建...")
            user_role = Role(name=RoleType.USER, description="普通用户")
            await role_repo.create_entity(user_role)
            await db.flush()  # 刷新会话，获取新创建角色的ID，但不提交事务

        # 将角色添加到用户的 role 列表中
        new_user.roles.append(user_role)

        # 添加用户并提交事务
        db.add(new_user)
        await db.commit()

        logger.info(f"用户 {user_data.username} 注册成功")
        return {"message": "User created successfully"}
    except Exception as e:
        logger.error(f"用户注册失败: {e}")
        raise HTTPException(status_code=500, detail=f"注册失败: {str(e)}")


@router.get("/debug/users")
async def debug_list_users(db: AsyncSessionDep):
    """调试端点：列出所有用户（仅用于开发环境）"""
    try:
        user_repo = UserRepository(db)
        users = await user_repo.get_all()

        user_list = []
        for user in users:
            user_list.append({
                "id": user.id,
                "username": user.username,
                "password_hash": user.password[:20] + "..." if user.password else None,  # 只显示前20个字符
                "created_at": user.created_at if hasattr(user, 'created_at') else None
            })

        return {
            "total_users": len(user_list),
            "users": user_list
        }
    except Exception as e:
        logger.error(f"获取用户列表失败: {e}")
        raise HTTPException(status_code=500, detail=f"获取用户列表失败: {str(e)}")


@router.post("/debug/test-password")
async def debug_test_password(
    username: str,
    password: str,
    db: AsyncSessionDep
):
    """调试端点：测试密码验证（仅用于开发环境）"""
    try:
        user_repo = UserRepository(db)
        user = await user_repo.get_one_by_username(username)

        if not user:
            return {
                "user_found": False,
                "message": f"用户 '{username}' 不存在"
            }

        # 测试密码验证
        is_valid = AuthService.verify_password(password, user.password)

        return {
            "user_found": True,
            "username": user.username,
            "password_hash": user.password[:20] + "..." if user.password else None,
            "password_valid": is_valid,
            "message": "密码正确" if is_valid else "密码错误"
        }

    except Exception as e:
        logger.error(f"密码测试失败: {e}")
        raise HTTPException(status_code=500, detail=f"密码测试失败: {str(e)}")
